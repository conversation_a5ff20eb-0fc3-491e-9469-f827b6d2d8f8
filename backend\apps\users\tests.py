from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from .models import Address

User = get_user_model()

class UserModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )

    def test_user_creation(self):
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.username, 'testuser')
        self.assertTrue(self.user.check_password('testpass123'))

    def test_user_str_method(self):
        self.assertEqual(str(self.user), '<EMAIL>')

class AuthAPITest(APITestCase):
    def test_user_registration(self):
        data = {
            'email': '<EMAIL>',
            'username': 'newuser',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
            'user_type': 'customer'
        }
        response = self.client.post('/api/auth/register/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

    def test_user_login(self):
        # Create user first
        user = User.objects.create_user(
            email='<EMAIL>',
            username='loginuser',
            password='loginpass123'
        )
        
        data = {
            'email': '<EMAIL>',
            'password': 'loginpass123'
        }
        response = self.client.post('/api/auth/login/', data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

class AddressModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.address = Address.objects.create(
            user=self.user,
            title='Home',
            address_line_1='123 Test Street',
            city='Test City',
            state='Test State',
            postal_code='12345'
        )

    def test_address_creation(self):
        self.assertEqual(self.address.title, 'Home')
        self.assertEqual(self.address.user, self.user)
        self.assertEqual(str(self.address), 'Home - <EMAIL>')
