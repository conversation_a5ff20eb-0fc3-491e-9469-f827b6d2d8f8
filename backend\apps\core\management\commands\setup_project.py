from django.core.management.base import BaseCommand
from django.core.management import call_command

class Command(BaseCommand):
    help = 'Complete project setup including migrations and sample data'

    def handle(self, *args, **options):
        self.stdout.write('Setting up YourPlace project...')
        
        # Make migrations
        self.stdout.write('Creating migrations...')
        call_command('makemigrations')
        
        # Apply migrations
        self.stdout.write('Applying migrations...')
        call_command('migrate')
        
        # Create sample data
        self.stdout.write('Creating sample data...')
        call_command('create_sample_data')
        
        # Collect static files
        self.stdout.write('Collecting static files...')
        call_command('collectstatic', '--noinput')
        
        self.stdout.write(self.style.SUCCESS('Project setup completed successfully!'))
        self.stdout.write('')
        self.stdout.write('You can now:')
        self.stdout.write('1. Create a superuser: python manage.py createsuperuser')
        self.stdout.write('2. Start the server: python manage.py runserver')
        self.stdout.write('3. Access admin at: http://localhost:8000/admin')
        self.stdout.write('4. Access API at: http://localhost:8000/api')
