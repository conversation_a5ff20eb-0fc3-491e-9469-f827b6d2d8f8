from rest_framework import serializers
from .models import Restaurant, FoodItem, Category, Review

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class FoodItemSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    
    class Meta:
        model = FoodItem
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at')

class RestaurantListSerializer(serializers.ModelSerializer):
    cuisine_types = CategorySerializer(many=True, read_only=True)
    
    class Meta:
        model = Restaurant
        fields = ('id', 'name', 'description', 'image', 'cuisine_types', 
                 'delivery_fee', 'delivery_time', 'rating', 'total_reviews',
                 'is_featured', 'city')

class RestaurantDetailSerializer(serializers.ModelSerializer):
    cuisine_types = CategorySerializer(many=True, read_only=True)
    food_items = FoodItemSerializer(many=True, read_only=True)
    owner_name = serializers.CharField(source='owner.get_full_name', read_only=True)
    
    class Meta:
        model = Restaurant
        fields = '__all__'
        read_only_fields = ('id', 'owner', 'rating', 'total_reviews', 'created_at', 'updated_at')

class ReviewSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = Review
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')

class RestaurantCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Restaurant
        fields = '__all__'
        read_only_fields = ('id', 'owner', 'rating', 'total_reviews', 'created_at', 'updated_at')
