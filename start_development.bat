@echo off
echo ========================================
echo    YourPlace Development Environment
echo ========================================
echo.

echo Checking if backend is set up...
if not exist "backend\venv" (
    echo Backend not set up. Running setup...
    call setup_backend.bat
)

echo.
echo Checking if frontend is set up...
if not exist "frontend\node_modules" (
    echo Frontend not set up. Running setup...
    call setup_frontend.bat
)

echo.
echo Starting development servers...
echo.

echo Starting Django backend on http://localhost:8000
start "YourPlace Backend" cmd /k "cd backend && venv\Scripts\activate && python manage.py runserver"

echo Waiting for backend to start...
timeout /t 5

echo Starting React frontend on http://localhost:3000
start "YourPlace Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo Development servers are starting...
echo.
echo Backend API: http://localhost:8000/api
echo Frontend App: http://localhost:3000
echo Django Admin: http://localhost:8000/admin
echo.
echo Default login credentials:
echo Customer: <EMAIL> / customer123
echo Admin: <EMAIL> / admin123
echo ========================================
echo.
echo Press any key to exit...
pause
