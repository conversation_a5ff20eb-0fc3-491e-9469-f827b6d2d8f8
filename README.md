# YourPlace - Food Delivery Application

A complete food delivery application similar to Zomato, built with React.js frontend and Django backend.

## Features

- User authentication and profiles
- Restaurant browsing and search
- Food item catalog with categories
- Shopping cart functionality
- Order management system
- Restaurant dashboard
- Admin panel
- Responsive design

## Tech Stack

### Frontend
- React.js 18
- React Router for navigation
- Axios for API calls
- Material-UI for beautiful components
- CSS3 with modern styling

### Backend
- Django 4.2
- Django REST Framework
- SQLite database (easily configurable to PostgreSQL/MySQL)
- JWT authentication
- Django CORS headers

## Project Structure

```
foodapp/
├── backend/                 # Django backend
│   ├── yourplace/          # Main Django project
│   ├── apps/               # Django apps
│   │   ├── users/          # User management
│   │   ├── restaurants/    # Restaurant management
│   │   ├── orders/         # Order management
│   │   └── core/           # Core utilities
│   ├── requirements.txt    # Python dependencies
│   └── manage.py
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── utils/          # Utilities
│   │   └── styles/         # CSS files
│   ├── public/
│   └── package.json
└── README.md
```

## Setup Instructions

### Backend Setup
1. Navigate to backend directory: `cd backend`
2. Create virtual environment: `python -m venv venv`
3. Activate virtual environment: `venv\Scripts\activate` (Windows) or `source venv/bin/activate` (Mac/Linux)
4. Install dependencies: `pip install -r requirements.txt`
5. Run migrations: `python manage.py migrate`
6. Create superuser: `python manage.py createsuperuser`
7. Start server: `python manage.py runserver`

### Frontend Setup
1. Navigate to frontend directory: `cd frontend`
2. Install dependencies: `npm install`
3. Start development server: `npm start`

## API Endpoints

- `/api/auth/` - Authentication endpoints
- `/api/users/` - User management
- `/api/restaurants/` - Restaurant operations
- `/api/food-items/` - Food item operations
- `/api/orders/` - Order management
- `/api/cart/` - Cart operations

## Default Credentials
- Admin: <EMAIL> / admin123
- User: <EMAIL> / user123

## Development

The application runs on:
- Backend: http://localhost:8000
- Frontend: http://localhost:3000

## License

MIT License
