import React from 'react';
import {
  Card,
  CardMedia,
  CardContent,
  Typography,
  Box,
  Chip,
  Rating,
} from '@mui/material';
import { AccessTime, DeliveryDining } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const RestaurantCard = ({ restaurant }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/restaurants/${restaurant.id}`);
  };

  return (
    <Card 
      className="card-hover" 
      onClick={handleClick}
      sx={{ 
        cursor: 'pointer',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <CardMedia
        component="img"
        height="200"
        image={restaurant.image || '/api/placeholder/400/200'}
        alt={restaurant.name}
        sx={{ objectFit: 'cover' }}
      />
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="h3" gutterBottom>
          {restaurant.name}
        </Typography>
        
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ 
            mb: 2,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
          }}
        >
          {restaurant.description}
        </Typography>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
          {restaurant.cuisine_types?.slice(0, 3).map((cuisine) => (
            <Chip
              key={cuisine.id}
              label={cuisine.name}
              size="small"
              variant="outlined"
              color="primary"
            />
          ))}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Rating value={restaurant.rating} readOnly size="small" />
          <Typography variant="body2" sx={{ ml: 1 }}>
            {restaurant.rating} ({restaurant.total_reviews} reviews)
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AccessTime fontSize="small" color="action" />
            <Typography variant="body2" sx={{ ml: 0.5 }}>
              {restaurant.delivery_time} min
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DeliveryDining fontSize="small" color="action" />
            <Typography variant="body2" sx={{ ml: 0.5 }}>
              ₹{restaurant.delivery_fee}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RestaurantCard;
