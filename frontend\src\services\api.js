import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          });
          
          const { access } = response.data;
          localStorage.setItem('access_token', access);
          
          return api(originalRequest);
        } catch (refreshError) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          window.location.href = '/login';
        }
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login/', credentials),
  register: (userData) => api.post('/auth/register/', userData),
  getProfile: () => api.get('/auth/profile/'),
  updateProfile: (data) => api.patch('/auth/profile/', data),
};

// Restaurant API
export const restaurantAPI = {
  getAll: (params) => api.get('/restaurants/', { params }),
  getById: (id) => api.get(`/restaurants/${id}/`),
  getFoodItems: (restaurantId) => api.get(`/restaurants/${restaurantId}/food-items/`),
  getCategories: () => api.get('/restaurants/categories/'),
  search: (query, city) => api.get('/restaurants/search/', { params: { q: query, city } }),
  getReviews: (restaurantId) => api.get(`/restaurants/${restaurantId}/reviews/`),
  addReview: (restaurantId, reviewData) => api.post(`/restaurants/${restaurantId}/reviews/`, reviewData),
};

// Cart API
export const cartAPI = {
  get: () => api.get('/orders/cart/'),
  addItem: (data) => api.post('/orders/cart/add/', data),
  updateItem: (itemId, data) => api.put(`/orders/cart/update/${itemId}/`, data),
  removeItem: (itemId) => api.delete(`/orders/cart/remove/${itemId}/`),
  clear: () => api.delete('/orders/cart/clear/'),
};

// Order API
export const orderAPI = {
  getAll: () => api.get('/orders/'),
  getById: (id) => api.get(`/orders/${id}/`),
  create: (data) => api.post('/orders/create/', data),
};

// Core API
export const coreAPI = {
  getHomeData: () => api.get('/home/'),
  getStats: () => api.get('/stats/'),
};

// Address API
export const addressAPI = {
  getAll: () => api.get('/auth/addresses/'),
  create: (data) => api.post('/auth/addresses/', data),
  update: (id, data) => api.patch(`/auth/addresses/${id}/`, data),
  delete: (id) => api.delete(`/auth/addresses/${id}/`),
};

export default api;
