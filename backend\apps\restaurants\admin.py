from django.contrib import admin
from .models import Restaurant, FoodItem, Category, Review

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name',)
    ordering = ('name',)

@admin.register(Restaurant)
class RestaurantAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'city', 'rating', 'is_active', 'is_featured', 'created_at')
    list_filter = ('is_active', 'is_featured', 'city', 'cuisine_types', 'created_at')
    search_fields = ('name', 'owner__email', 'city', 'description')
    filter_horizontal = ('cuisine_types',)
    ordering = ('-created_at',)

@admin.register(FoodItem)
class FoodItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'restaurant', 'price', 'food_type', 'is_available', 'is_featured')
    list_filter = ('food_type', 'is_available', 'is_featured', 'category', 'created_at')
    search_fields = ('name', 'restaurant__name', 'description')
    ordering = ('-created_at',)

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('user', 'restaurant', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('user__email', 'restaurant__name', 'comment')
    ordering = ('-created_at',)
