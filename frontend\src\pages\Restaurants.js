import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import { Search, FilterList } from '@mui/icons-material';
import { restaurantAPI } from '../services/api';
import RestaurantCard from '../components/RestaurantCard';

const Restaurants = () => {
  const [restaurants, setRestaurants] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    city: '',
    ordering: '-rating',
  });

  useEffect(() => {
    fetchCategories();
    fetchRestaurants();
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchRestaurants();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [filters]);

  const fetchCategories = async () => {
    try {
      const response = await restaurantAPI.getCategories();
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchRestaurants = async () => {
    try {
      setLoading(true);
      const params = {};
      
      if (filters.search) params.search = filters.search;
      if (filters.category) params.cuisine_types = filters.category;
      if (filters.city) params.city = filters.city;
      if (filters.ordering) params.ordering = filters.ordering;

      const response = await restaurantAPI.getAll(params);
      setRestaurants(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching restaurants:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters({
      ...filters,
      [field]: value,
    });
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      city: '',
      ordering: '-rating',
    });
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
        Restaurants
      </Typography>

      {/* Filters */}
      <Paper elevation={1} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <FilterList sx={{ mr: 1 }} />
          <Typography variant="h6">Filters</Typography>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Search restaurants"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              label="City"
              value={filters.city}
              onChange={(e) => handleFilterChange('city', e.target.value)}
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Sort By</InputLabel>
              <Select
                value={filters.ordering}
                onChange={(e) => handleFilterChange('ordering', e.target.value)}
                label="Sort By"
              >
                <MenuItem value="-rating">Rating (High to Low)</MenuItem>
                <MenuItem value="delivery_time">Delivery Time</MenuItem>
                <MenuItem value="delivery_fee">Delivery Fee</MenuItem>
                <MenuItem value="-created_at">Newest First</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              onClick={clearFilters}
              sx={{ height: '56px' }}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>

        {/* Active Filters */}
        <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {filters.search && (
            <Chip
              label={`Search: ${filters.search}`}
              onDelete={() => handleFilterChange('search', '')}
              color="primary"
              variant="outlined"
            />
          )}
          {filters.category && (
            <Chip
              label={`Category: ${categories.find(c => c.id.toString() === filters.category)?.name}`}
              onDelete={() => handleFilterChange('category', '')}
              color="primary"
              variant="outlined"
            />
          )}
          {filters.city && (
            <Chip
              label={`City: ${filters.city}`}
              onDelete={() => handleFilterChange('city', '')}
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
      </Paper>

      {/* Results */}
      {loading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Typography variant="h6" sx={{ mb: 3 }}>
            {restaurants.length} restaurant{restaurants.length !== 1 ? 's' : ''} found
          </Typography>
          
          <Grid container spacing={3}>
            {restaurants.map((restaurant) => (
              <Grid item xs={12} sm={6} md={4} key={restaurant.id}>
                <RestaurantCard restaurant={restaurant} />
              </Grid>
            ))}
          </Grid>

          {restaurants.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" color="text.secondary">
                No restaurants found matching your criteria
              </Typography>
              <Button
                variant="outlined"
                onClick={clearFilters}
                sx={{ mt: 2 }}
              >
                Clear Filters
              </Button>
            </Box>
          )}
        </>
      )}
    </Container>
  );
};

export default Restaurants;
