# YourPlace - Complete Food Delivery Application

## 🚀 Project Overview

YourPlace is a comprehensive food delivery application similar to Zomato, built with modern web technologies. It provides a complete ecosystem for customers, restaurant owners, and administrators.

## ✨ Key Features

### For Customers
- **User Registration & Authentication** - Secure JWT-based authentication
- **Restaurant Discovery** - Browse restaurants by category, location, and ratings
- **Advanced Search & Filters** - Find restaurants and food items easily
- **Menu Browsing** - View detailed restaurant menus with images and descriptions
- **Shopping Cart** - Add items, modify quantities, special instructions
- **Multiple Addresses** - Manage delivery addresses
- **Order Management** - Place orders, track status, view history
- **Reviews & Ratings** - Rate restaurants and leave feedback
- **Responsive Design** - Works perfectly on mobile and desktop

### For Restaurant Owners
- **Restaurant Profile** - Manage restaurant information and images
- **Menu Management** - Add, edit, and organize food items
- **Order Processing** - View and manage incoming orders
- **Business Analytics** - Track performance and customer feedback

### For Administrators
- **User Management** - Manage all user accounts
- **Restaurant Approval** - Approve new restaurant registrations
- **Category Management** - Manage food categories
- **System Overview** - Monitor platform activity and statistics

## 🛠 Technology Stack

### Backend (Django)
- **Django 4.2** - Robust web framework
- **Django REST Framework** - Powerful API development
- **JWT Authentication** - Secure token-based auth
- **SQLite Database** - Easy development (PostgreSQL ready)
- **Django Admin** - Built-in admin interface
- **CORS Support** - Cross-origin resource sharing

### Frontend (React)
- **React 18** - Modern frontend framework
- **Material-UI** - Beautiful, responsive components
- **React Router** - Client-side navigation
- **Context API** - State management
- **Axios** - HTTP client with interceptors
- **Responsive Design** - Mobile-first approach

## 📁 Project Structure

```
foodapp/
├── backend/                    # Django Backend
│   ├── yourplace/             # Main Django project
│   │   ├── settings.py        # Django settings
│   │   ├── urls.py           # URL routing
│   │   └── wsgi.py           # WSGI configuration
│   ├── apps/                  # Django applications
│   │   ├── users/            # User management & authentication
│   │   │   ├── models.py     # User and Address models
│   │   │   ├── views.py      # Authentication views
│   │   │   ├── serializers.py # API serializers
│   │   │   └── urls.py       # URL patterns
│   │   ├── restaurants/      # Restaurant & food management
│   │   │   ├── models.py     # Restaurant, FoodItem, Category models
│   │   │   ├── views.py      # Restaurant API views
│   │   │   ├── serializers.py # API serializers
│   │   │   └── urls.py       # URL patterns
│   │   ├── orders/           # Cart & order management
│   │   │   ├── models.py     # Cart, Order models
│   │   │   ├── views.py      # Order processing views
│   │   │   ├── serializers.py # API serializers
│   │   │   └── urls.py       # URL patterns
│   │   └── core/             # Core utilities
│   │       ├── views.py      # Home page data
│   │       ├── management/   # Management commands
│   │       └── urls.py       # URL patterns
│   ├── requirements.txt       # Python dependencies
│   └── manage.py             # Django management script
├── frontend/                  # React Frontend
│   ├── src/
│   │   ├── components/       # Reusable components
│   │   │   ├── Navbar.js     # Navigation bar
│   │   │   ├── RestaurantCard.js # Restaurant display card
│   │   │   ├── FoodItemCard.js   # Food item display card
│   │   │   └── ProtectedRoute.js # Route protection
│   │   ├── pages/            # Page components
│   │   │   ├── Home.js       # Landing page
│   │   │   ├── Login.js      # User login
│   │   │   ├── Register.js   # User registration
│   │   │   ├── Restaurants.js # Restaurant listing
│   │   │   ├── RestaurantDetail.js # Restaurant details
│   │   │   ├── Cart.js       # Shopping cart
│   │   │   ├── Checkout.js   # Order checkout
│   │   │   ├── Orders.js     # Order history
│   │   │   └── Profile.js    # User profile
│   │   ├── contexts/         # React contexts
│   │   │   ├── AuthContext.js # Authentication state
│   │   │   └── CartContext.js # Cart state management
│   │   ├── services/         # API services
│   │   │   └── api.js        # API client configuration
│   │   └── utils/            # Utility functions
│   │       ├── constants.js  # App constants
│   │       └── helpers.js    # Helper functions
│   ├── public/               # Static files
│   └── package.json          # Node.js dependencies
├── setup_backend.bat         # Backend setup script
├── setup_frontend.bat        # Frontend setup script
├── start_development.bat     # Development server starter
├── docker-compose.yml        # Docker configuration
└── README.md                 # Project documentation
```

## 🎨 Design Features

- **Modern UI/UX** - Clean, intuitive interface with Material Design
- **Responsive Layout** - Optimized for all screen sizes
- **Beautiful Cards** - Attractive restaurant and food item displays
- **Smooth Animations** - Hover effects and transitions
- **Color Scheme** - Orange/red primary colors (food industry standard)
- **Typography** - Inter font for modern, readable text
- **Loading States** - Proper loading indicators throughout the app

## 🔧 Setup Instructions

### Quick Start (Recommended)
1. Run `start_development.bat` - This will set up and start everything automatically

### Manual Setup
1. **Backend**: Run `setup_backend.bat` or follow manual steps in SETUP_GUIDE.md
2. **Frontend**: Run `setup_frontend.bat` or follow manual steps in SETUP_GUIDE.md

## 🧪 Testing

### Backend Tests
```bash
cd backend
python manage.py test
```

### Frontend Tests
```bash
cd frontend
npm test
```

## 🚀 Deployment

The project includes Docker configuration for easy deployment:

```bash
docker-compose up --build
```

## 📊 Sample Data

The project includes sample data with:
- 3 restaurants with different cuisines
- 6+ food items with various categories
- Test user accounts
- Sample addresses and categories

## 🔐 Security Features

- JWT token authentication with refresh tokens
- Password validation and hashing
- CORS protection
- SQL injection protection via Django ORM
- XSS protection
- CSRF protection

## 📱 Mobile Responsive

The application is fully responsive and works seamlessly on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🎯 Future Enhancements

- Real-time order tracking
- Push notifications
- Payment gateway integration
- Google Maps integration
- Advanced analytics dashboard
- Multi-language support
- Dark mode theme
