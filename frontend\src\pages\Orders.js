import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Chip,
  Card,
  CardContent,
  CardMedia,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Receipt, AccessTime } from '@mui/icons-material';
import { useLocation } from 'react-router-dom';
import { orderAPI } from '../services/api';

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await orderAPI.getAll();
      setOrders(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'confirmed': return 'info';
      case 'preparing': return 'primary';
      case 'out_for_delivery': return 'secondary';
      case 'delivered': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
        <Receipt sx={{ mr: 1, verticalAlign: 'middle' }} />
        My Orders
      </Typography>

      {location.state?.message && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {location.state.message}
        </Alert>
      )}

      {orders.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Receipt sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            No orders yet
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Your order history will appear here
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {orders.map((order) => (
            <Grid item xs={12} key={order.id}>
              <Card elevation={2} sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={3}>
                      <CardMedia
                        component="img"
                        height="120"
                        image={order.restaurant_image || '/api/placeholder/200/120'}
                        alt={order.restaurant_name}
                        sx={{ borderRadius: 1, objectFit: 'cover' }}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Typography variant="h6" gutterBottom>
                        {order.restaurant_name}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Order #{order.order_number}
                      </Typography>
                      
                      <Box sx={{ mb: 2 }}>
                        {order.items?.slice(0, 3).map((item, index) => (
                          <Typography key={index} variant="body2">
                            {item.food_item_name} x {item.quantity}
                          </Typography>
                        ))}
                        {order.items?.length > 3 && (
                          <Typography variant="body2" color="text.secondary">
                            +{order.items.length - 3} more items
                          </Typography>
                        )}
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <AccessTime fontSize="small" sx={{ mr: 1 }} />
                        <Typography variant="body2">
                          Ordered on {new Date(order.created_at).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </Grid>
                    
                    <Grid item xs={12} sm={3}>
                      <Box sx={{ textAlign: { xs: 'left', sm: 'right' } }}>
                        <Chip
                          label={formatStatus(order.status)}
                          color={getStatusColor(order.status)}
                          sx={{ mb: 2 }}
                        />
                        
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          ₹{order.total_amount}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary">
                          via {formatStatus(order.payment_method)}
                        </Typography>
                        
                        {order.estimated_delivery_time && (
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            Est. delivery: {new Date(order.estimated_delivery_time).toLocaleTimeString()}
                          </Typography>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                  
                  {order.special_instructions && (
                    <>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                        Special instructions: {order.special_instructions}
                      </Typography>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default Orders;
