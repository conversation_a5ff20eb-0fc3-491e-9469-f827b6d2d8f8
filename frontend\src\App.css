/* Global Styles */
.App {
  min-height: 100vh;
}

/* Custom animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

/* Custom utility classes */
.gradient-bg {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.card-hover {
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* Food type indicators */
.food-type-veg {
  color: #4caf50;
}

.food-type-non-veg {
  color: #f44336;
}

.food-type-vegan {
  color: #2196f3;
}

/* Status indicators */
.status-pending {
  color: #ff9800;
}

.status-confirmed {
  color: #2196f3;
}

.status-preparing {
  color: #9c27b0;
}

.status-out-for-delivery {
  color: #ff5722;
}

.status-delivered {
  color: #4caf50;
}

.status-cancelled {
  color: #f44336;
}

/* Responsive design helpers */
@media (max-width: 600px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
