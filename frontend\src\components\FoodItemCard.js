import React, { useState } from 'react';
import {
  Card,
  CardMedia,
  CardContent,
  Typography,
  Box,
  Button,
  Chip,
  IconButton,
  Snackbar,
  Alert,
} from '@mui/material';
import { Add, Remove } from '@mui/icons-material';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';

const FoodItemCard = ({ foodItem }) => {
  const [quantity, setQuantity] = useState(1);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const { addToCart } = useCart();
  const { isAuthenticated } = useAuth();

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      setSnackbar({
        open: true,
        message: 'Please login to add items to cart',
        severity: 'warning'
      });
      return;
    }

    const result = await addToCart(foodItem.id, quantity);
    if (result.success) {
      setSnackbar({
        open: true,
        message: 'Item added to cart successfully!',
        severity: 'success'
      });
      setQuantity(1);
    } else {
      setSnackbar({
        open: true,
        message: 'Failed to add item to cart',
        severity: 'error'
      });
    }
  };

  const getFoodTypeColor = (type) => {
    switch (type) {
      case 'veg': return 'success';
      case 'non_veg': return 'error';
      case 'vegan': return 'primary';
      default: return 'default';
    }
  };

  const getFoodTypeIcon = (type) => {
    switch (type) {
      case 'veg': return '🟢';
      case 'non_veg': return '🔴';
      case 'vegan': return '🌱';
      default: return '';
    }
  };

  return (
    <>
      <Card className="card-hover" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardMedia
          component="img"
          height="180"
          image={foodItem.image || '/api/placeholder/300/180'}
          alt={foodItem.name}
          sx={{ objectFit: 'cover' }}
        />
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="h3" sx={{ flexGrow: 1 }}>
              {foodItem.name}
            </Typography>
            <Chip
              label={`${getFoodTypeIcon(foodItem.food_type)} ${foodItem.food_type.replace('_', ' ')}`}
              size="small"
              color={getFoodTypeColor(foodItem.food_type)}
              variant="outlined"
            />
          </Box>
          
          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ 
              mb: 2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {foodItem.description}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
              ₹{foodItem.final_price}
            </Typography>
            {foodItem.discounted_price && (
              <Typography 
                variant="body2" 
                sx={{ 
                  ml: 1, 
                  textDecoration: 'line-through',
                  color: 'text.secondary'
                }}
              >
                ₹{foodItem.price}
              </Typography>
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton 
                size="small" 
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                <Remove />
              </IconButton>
              <Typography sx={{ mx: 1, minWidth: '20px', textAlign: 'center' }}>
                {quantity}
              </Typography>
              <IconButton 
                size="small" 
                onClick={() => setQuantity(quantity + 1)}
              >
                <Add />
              </IconButton>
            </Box>
            
            <Button
              variant="contained"
              size="small"
              onClick={handleAddToCart}
              disabled={!foodItem.is_available}
            >
              {foodItem.is_available ? 'Add to Cart' : 'Unavailable'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default FoodItemCard;
