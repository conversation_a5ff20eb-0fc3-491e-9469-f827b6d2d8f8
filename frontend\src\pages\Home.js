import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  CircularProgress,
} from '@mui/material';
import { Restaurant, LocalDining, Category } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { coreAPI } from '../services/api';
import RestaurantCard from '../components/RestaurantCard';
import FoodItemCard from '../components/FoodItemCard';

const Home = () => {
  const [homeData, setHomeData] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchHomeData();
  }, []);

  const fetchHomeData = async () => {
    try {
      const response = await coreAPI.getHomeData();
      setHomeData(response.data);
    } catch (error) {
      console.error('Error fetching home data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
          borderRadius: 3,
          color: 'white',
          p: 6,
          mb: 6,
          textAlign: 'center',
        }}
      >
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
          Welcome to YourPlace
        </Typography>
        <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
          Discover and order from the best restaurants in your city
        </Typography>
        <Button
          variant="contained"
          size="large"
          onClick={() => navigate('/restaurants')}
          sx={{
            bgcolor: 'white',
            color: 'primary.main',
            '&:hover': { bgcolor: 'grey.100' },
            px: 4,
            py: 1.5,
          }}
        >
          Explore Restaurants
        </Button>
      </Box>

      {/* Categories Section */}
      {homeData?.categories?.length > 0 && (
        <Box sx={{ mb: 6 }}>
          <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
            <Category sx={{ mr: 1, verticalAlign: 'middle' }} />
            Browse by Category
          </Typography>
          <Grid container spacing={2}>
            {homeData.categories.map((category) => (
              <Grid item xs={6} sm={4} md={3} key={category.id}>
                <Card 
                  className="card-hover"
                  sx={{ 
                    cursor: 'pointer',
                    textAlign: 'center',
                    p: 2,
                  }}
                  onClick={() => navigate(`/restaurants?category=${category.id}`)}
                >
                  <CardMedia
                    component="img"
                    height="80"
                    image={category.image || '/api/placeholder/100/80'}
                    alt={category.name}
                    sx={{ objectFit: 'cover', borderRadius: 1, mb: 1 }}
                  />
                  <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                    {category.name}
                  </Typography>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Featured Restaurants */}
      {homeData?.featured_restaurants?.length > 0 && (
        <Box sx={{ mb: 6 }}>
          <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
            <Restaurant sx={{ mr: 1, verticalAlign: 'middle' }} />
            Featured Restaurants
          </Typography>
          <Grid container spacing={3}>
            {homeData.featured_restaurants.map((restaurant) => (
              <Grid item xs={12} sm={6} md={4} key={restaurant.id}>
                <RestaurantCard restaurant={restaurant} />
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Popular Food Items */}
      {homeData?.popular_food_items?.length > 0 && (
        <Box>
          <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
            <LocalDining sx={{ mr: 1, verticalAlign: 'middle' }} />
            Popular Dishes
          </Typography>
          <Grid container spacing={3}>
            {homeData.popular_food_items.map((foodItem) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={foodItem.id}>
                <FoodItemCard foodItem={foodItem} />
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Container>
  );
};

export default Home;
