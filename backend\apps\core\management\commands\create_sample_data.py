from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.restaurants.models import Category, Restaurant, FoodItem
from apps.users.models import Address
from datetime import time

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample data for the application'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create admin user
        admin_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'admin',
                'first_name': 'Admin',
                'last_name': 'User',
                'user_type': 'admin',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write('Admin user created')

        # Create sample customer
        customer, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'customer',
                'first_name': '<PERSON>',
                'last_name': 'Doe',
                'phone': '+1234567890',
                'user_type': 'customer',
            }
        )
        if created:
            customer.set_password('customer123')
            customer.save()
            self.stdout.write('Customer user created')

        # Create restaurant owner
        owner, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'username': 'restaurant_owner',
                'first_name': 'Restaurant',
                'last_name': 'Owner',
                'phone': '+1234567891',
                'user_type': 'restaurant_owner',
            }
        )
        if created:
            owner.set_password('owner123')
            owner.save()
            self.stdout.write('Restaurant owner created')

        # Create categories
        categories_data = [
            {'name': 'Italian', 'description': 'Authentic Italian cuisine'},
            {'name': 'Chinese', 'description': 'Traditional Chinese dishes'},
            {'name': 'Indian', 'description': 'Spicy and flavorful Indian food'},
            {'name': 'Fast Food', 'description': 'Quick and tasty fast food'},
            {'name': 'Desserts', 'description': 'Sweet treats and desserts'},
            {'name': 'Beverages', 'description': 'Refreshing drinks and beverages'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                self.stdout.write(f'Category {category.name} created')

        # Create sample restaurants
        italian_cat = Category.objects.get(name='Italian')
        chinese_cat = Category.objects.get(name='Chinese')
        indian_cat = Category.objects.get(name='Indian')
        
        restaurants_data = [
            {
                'name': 'Pasta Palace',
                'description': 'Authentic Italian pasta and pizza',
                'phone': '+1234567892',
                'email': '<EMAIL>',
                'address_line_1': '123 Main Street',
                'city': 'Mumbai',
                'state': 'Maharashtra',
                'postal_code': '400001',
                'opening_time': time(10, 0),
                'closing_time': time(23, 0),
                'delivery_fee': 50.00,
                'minimum_order': 200.00,
                'delivery_time': 30,
                'is_featured': True,
                'rating': 4.5,
                'total_reviews': 150,
                'cuisine_types': [italian_cat],
            },
            {
                'name': 'Dragon Wok',
                'description': 'Best Chinese food in town',
                'phone': '+1234567893',
                'email': '<EMAIL>',
                'address_line_1': '456 Food Street',
                'city': 'Delhi',
                'state': 'Delhi',
                'postal_code': '110001',
                'opening_time': time(11, 0),
                'closing_time': time(22, 30),
                'delivery_fee': 40.00,
                'minimum_order': 150.00,
                'delivery_time': 25,
                'is_featured': True,
                'rating': 4.2,
                'total_reviews': 89,
                'cuisine_types': [chinese_cat],
            },
            {
                'name': 'Spice Garden',
                'description': 'Traditional Indian flavors',
                'phone': '+1234567894',
                'email': '<EMAIL>',
                'address_line_1': '789 Curry Lane',
                'city': 'Bangalore',
                'state': 'Karnataka',
                'postal_code': '560001',
                'opening_time': time(9, 0),
                'closing_time': time(23, 30),
                'delivery_fee': 35.00,
                'minimum_order': 180.00,
                'delivery_time': 35,
                'is_featured': False,
                'rating': 4.7,
                'total_reviews': 203,
                'cuisine_types': [indian_cat],
            },
        ]
        
        for rest_data in restaurants_data:
            cuisine_types = rest_data.pop('cuisine_types')
            restaurant, created = Restaurant.objects.get_or_create(
                name=rest_data['name'],
                defaults={**rest_data, 'owner': owner}
            )
            if created:
                restaurant.cuisine_types.set(cuisine_types)
                self.stdout.write(f'Restaurant {restaurant.name} created')

        # Create sample food items
        pasta_palace = Restaurant.objects.get(name='Pasta Palace')
        dragon_wok = Restaurant.objects.get(name='Dragon Wok')
        spice_garden = Restaurant.objects.get(name='Spice Garden')

        food_items_data = [
            # Pasta Palace items
            {
                'restaurant': pasta_palace,
                'category': italian_cat,
                'name': 'Margherita Pizza',
                'description': 'Classic pizza with fresh tomatoes, mozzarella, and basil',
                'price': 299.00,
                'food_type': 'veg',
                'preparation_time': 20,
                'is_featured': True,
            },
            {
                'restaurant': pasta_palace,
                'category': italian_cat,
                'name': 'Chicken Alfredo Pasta',
                'description': 'Creamy pasta with grilled chicken and parmesan cheese',
                'price': 349.00,
                'food_type': 'non_veg',
                'preparation_time': 25,
                'is_featured': True,
            },
            # Dragon Wok items
            {
                'restaurant': dragon_wok,
                'category': chinese_cat,
                'name': 'Chicken Fried Rice',
                'description': 'Wok-tossed rice with chicken and vegetables',
                'price': 249.00,
                'food_type': 'non_veg',
                'preparation_time': 15,
                'is_featured': True,
            },
            {
                'restaurant': dragon_wok,
                'category': chinese_cat,
                'name': 'Veg Hakka Noodles',
                'description': 'Stir-fried noodles with fresh vegetables',
                'price': 199.00,
                'food_type': 'veg',
                'preparation_time': 12,
            },
            # Spice Garden items
            {
                'restaurant': spice_garden,
                'category': indian_cat,
                'name': 'Butter Chicken',
                'description': 'Creamy tomato-based chicken curry',
                'price': 329.00,
                'food_type': 'non_veg',
                'preparation_time': 20,
                'is_featured': True,
            },
            {
                'restaurant': spice_garden,
                'category': indian_cat,
                'name': 'Paneer Tikka Masala',
                'description': 'Grilled cottage cheese in spicy tomato gravy',
                'price': 279.00,
                'food_type': 'veg',
                'preparation_time': 18,
            },
        ]

        for item_data in food_items_data:
            food_item, created = FoodItem.objects.get_or_create(
                name=item_data['name'],
                restaurant=item_data['restaurant'],
                defaults=item_data
            )
            if created:
                self.stdout.write(f'Food item {food_item.name} created')

        # Create sample address for customer
        address, created = Address.objects.get_or_create(
            user=customer,
            title='Home',
            defaults={
                'address_line_1': '123 Customer Street',
                'city': 'Mumbai',
                'state': 'Maharashtra',
                'postal_code': '400001',
                'is_default': True,
            }
        )
        if created:
            self.stdout.write('Sample address created')

        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
