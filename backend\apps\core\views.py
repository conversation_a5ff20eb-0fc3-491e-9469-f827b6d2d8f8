from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from apps.restaurants.models import Restaurant, FoodItem, Category
from apps.restaurants.serializers import RestaurantListSerializer, FoodItemSerializer, CategorySerializer

@api_view(['GET'])
@permission_classes([AllowAny])
def home_data(request):
    """
    API endpoint to get home page data including featured restaurants,
    popular food items, and categories
    """
    featured_restaurants = Restaurant.objects.filter(
        is_active=True, 
        is_featured=True
    ).order_by('-rating')[:6]
    
    popular_food_items = FoodItem.objects.filter(
        is_available=True,
        is_featured=True
    ).order_by('-restaurant__rating')[:8]
    
    categories = Category.objects.filter(is_active=True)[:8]
    
    data = {
        'featured_restaurants': RestaurantListSerializer(featured_restaurants, many=True).data,
        'popular_food_items': FoodItemSerializer(popular_food_items, many=True).data,
        'categories': CategorySerializer(categories, many=True).data,
    }
    
    return Response(data)

@api_view(['GET'])
@permission_classes([AllowAny])
def app_stats(request):
    """
    API endpoint to get application statistics
    """
    stats = {
        'total_restaurants': Restaurant.objects.filter(is_active=True).count(),
        'total_food_items': FoodItem.objects.filter(is_available=True).count(),
        'total_categories': Category.objects.filter(is_active=True).count(),
    }
    
    return Response(stats)
