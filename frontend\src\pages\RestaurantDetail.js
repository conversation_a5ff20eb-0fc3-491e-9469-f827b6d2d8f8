import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  Chip,
  Rating,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  AccessTime,
  DeliveryDining,
  Phone,
  Email,
  LocationOn,
} from '@mui/icons-material';
import { useParams } from 'react-router-dom';
import { restaurantAPI } from '../services/api';
import FoodItemCard from '../components/FoodItemCard';

const RestaurantDetail = () => {
  const { id } = useParams();
  const [restaurant, setRestaurant] = useState(null);
  const [foodItems, setFoodItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRestaurantData();
  }, [id]);

  const fetchRestaurantData = async () => {
    try {
      setLoading(true);
      const [restaurantResponse, foodItemsResponse] = await Promise.all([
        restaurantAPI.getById(id),
        restaurantAPI.getFoodItems(id),
      ]);
      
      setRestaurant(restaurantResponse.data);
      setFoodItems(foodItemsResponse.data.results || foodItemsResponse.data);
    } catch (error) {
      console.error('Error fetching restaurant data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (!restaurant) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h5" textAlign="center">
          Restaurant not found
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Restaurant Header */}
      <Paper elevation={2} sx={{ borderRadius: 3, overflow: 'hidden', mb: 4 }}>
        <Box
          sx={{
            height: 300,
            backgroundImage: `url(${restaurant.image || '/api/placeholder/800/300'})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            position: 'relative',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
              color: 'white',
              p: 3,
            }}
          >
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 1 }}>
              {restaurant.name}
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              {restaurant.description}
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Restaurant Info */}
      <Grid container spacing={4} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              Restaurant Information
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
              {restaurant.cuisine_types?.map((cuisine) => (
                <Chip
                  key={cuisine.id}
                  label={cuisine.name}
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Rating value={restaurant.rating} readOnly />
              <Typography sx={{ ml: 1 }}>
                {restaurant.rating} ({restaurant.total_reviews} reviews)
              </Typography>
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AccessTime fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    {restaurant.opening_time} - {restaurant.closing_time}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <DeliveryDining fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="body2">
                    {restaurant.delivery_time} min • ₹{restaurant.delivery_fee} delivery
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Phone fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="body2">{restaurant.phone}</Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Email fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="body2">{restaurant.email}</Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              <LocationOn sx={{ mr: 1, verticalAlign: 'middle' }} />
              Address
            </Typography>
            <Typography variant="body2">
              {restaurant.address_line_1}
              {restaurant.address_line_2 && <><br />{restaurant.address_line_2}</>}
              <br />
              {restaurant.city}, {restaurant.state} {restaurant.postal_code}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      {/* Food Items */}
      <Typography variant="h5" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
        Menu
      </Typography>
      
      {foodItems.length > 0 ? (
        <Grid container spacing={3}>
          {foodItems.map((foodItem) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={foodItem.id}>
              <FoodItemCard foodItem={foodItem} />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            No menu items available at the moment
          </Typography>
        </Box>
      )}
    </Container>
  );
};

export default RestaurantDetail;
