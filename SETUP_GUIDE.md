# YourPlace Setup Guide

## Prerequisites
- Python 3.8+ installed
- Node.js 16+ installed
- Git installed

## Quick Setup (Windows)

### Option 1: Automated Setup
1. Run `setup_backend.bat` to set up the Django backend
2. Run `setup_frontend.bat` to set up the React frontend
3. Run `run_project.bat` to start both servers

### Option 2: Manual Setup

#### Backend Setup
1. Open Command Prompt/PowerShell
2. Navigate to the project directory
3. Run the following commands:

```bash
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
python manage.py makemigrations
python manage.py migrate
python manage.py create_sample_data
python manage.py createsuperuser
python manage.py runserver
```

#### Frontend Setup
1. Open a new Command Prompt/PowerShell window
2. Navigate to the project directory
3. Run the following commands:

```bash
cd frontend
npm install
npm start
```

## Default Users
After running the sample data command, you'll have these test accounts:

- **Admin**: <EMAIL> / admin123
- **Customer**: <EMAIL> / customer123
- **Restaurant Owner**: <EMAIL> / owner123

## Application URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000/api
- **Django Admin**: http://localhost:8000/admin

## Features Included

### Customer Features
- User registration and authentication
- Browse restaurants by category, location, rating
- Search restaurants and food items
- View restaurant details and menu
- Add items to cart with quantity and special instructions
- Manage delivery addresses
- Place orders with multiple payment options
- View order history and status
- Rate and review restaurants

### Restaurant Owner Features
- Restaurant profile management
- Menu management (add/edit food items)
- Order management
- View analytics and reports

### Admin Features
- User management
- Restaurant approval and management
- Category management
- Order oversight
- System analytics

## Technology Stack

### Backend
- **Django 4.2** - Web framework
- **Django REST Framework** - API development
- **SQLite** - Database (easily configurable to PostgreSQL/MySQL)
- **JWT Authentication** - Secure authentication
- **Django CORS** - Cross-origin resource sharing

### Frontend
- **React 18** - Frontend framework
- **Material-UI** - UI component library
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **Context API** - State management

## Project Structure
```
foodapp/
├── backend/                 # Django backend
│   ├── yourplace/          # Django project settings
│   ├── apps/               # Django applications
│   │   ├── users/          # User management
│   │   ├── restaurants/    # Restaurant and food management
│   │   ├── orders/         # Cart and order management
│   │   └── core/           # Core utilities and home data
│   ├── requirements.txt    # Python dependencies
│   └── manage.py          # Django management script
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # Reusable React components
│   │   ├── pages/          # Page components
│   │   ├── contexts/       # React context providers
│   │   ├── services/       # API service functions
│   │   └── utils/          # Utility functions
│   ├── public/            # Static files
│   └── package.json       # Node.js dependencies
├── setup_backend.bat      # Backend setup script
├── setup_frontend.bat     # Frontend setup script
├── run_project.bat        # Start both servers
└── README.md             # Project documentation
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   - Backend: Change port in `python manage.py runserver 8001`
   - Frontend: Set PORT=3001 in environment or use different port

2. **Database errors**
   - Delete `db.sqlite3` and run migrations again
   - Check if all apps are properly installed in settings.py

3. **CORS errors**
   - Ensure django-cors-headers is installed
   - Check CORS_ALLOWED_ORIGINS in settings.py

4. **Module not found errors**
   - Ensure virtual environment is activated
   - Reinstall requirements: `pip install -r requirements.txt`

## Next Steps

1. **Customize the design** - Modify colors, fonts, and layout in the React components
2. **Add more features** - Implement real-time order tracking, push notifications
3. **Deploy** - Set up production deployment on cloud platforms
4. **Add payment integration** - Integrate with payment gateways like Stripe or Razorpay
5. **Add maps** - Integrate Google Maps for location services
6. **Add analytics** - Implement user behavior tracking and business analytics

## Support
For issues and questions, refer to the documentation or create an issue in the project repository.
