from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timedelta
import uuid
from .models import Cart, CartItem, Order, OrderItem
from .serializers import (
    CartSerializer, 
    CartItemSerializer, 
    CartItemCreateSerializer,
    OrderSerializer,
    OrderCreateSerializer
)
from apps.restaurants.models import FoodItem
from apps.users.models import Address

class CartView(generics.RetrieveAPIView):
    serializer_class = CartSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        cart, created = Cart.objects.get_or_create(user=self.request.user)
        return cart

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_to_cart(request):
    cart, created = Cart.objects.get_or_create(user=request.user)
    serializer = CartItemCreateSerializer(data=request.data)
    
    if serializer.is_valid():
        food_item = serializer.validated_data['food_item']
        quantity = serializer.validated_data['quantity']
        special_instructions = serializer.validated_data.get('special_instructions', '')
        
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            food_item=food_item,
            defaults={
                'quantity': quantity,
                'special_instructions': special_instructions
            }
        )
        
        if not created:
            cart_item.quantity += quantity
            cart_item.special_instructions = special_instructions
            cart_item.save()
        
        return Response(CartItemSerializer(cart_item).data, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_cart_item(request, item_id):
    cart_item = get_object_or_404(CartItem, id=item_id, cart__user=request.user)
    
    quantity = request.data.get('quantity', cart_item.quantity)
    special_instructions = request.data.get('special_instructions', cart_item.special_instructions)
    
    if quantity <= 0:
        cart_item.delete()
        return Response({'message': 'Item removed from cart'}, status=status.HTTP_204_NO_CONTENT)
    
    cart_item.quantity = quantity
    cart_item.special_instructions = special_instructions
    cart_item.save()
    
    return Response(CartItemSerializer(cart_item).data)

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_from_cart(request, item_id):
    cart_item = get_object_or_404(CartItem, id=item_id, cart__user=request.user)
    cart_item.delete()
    return Response({'message': 'Item removed from cart'}, status=status.HTTP_204_NO_CONTENT)

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def clear_cart(request):
    cart = get_object_or_404(Cart, user=request.user)
    cart.items.all().delete()
    return Response({'message': 'Cart cleared'}, status=status.HTTP_204_NO_CONTENT)

class OrderListView(generics.ListAPIView):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)

class OrderDetailView(generics.RetrieveAPIView):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_order(request):
    cart = get_object_or_404(Cart, user=request.user)
    
    if not cart.items.exists():
        return Response({'error': 'Cart is empty'}, status=status.HTTP_400_BAD_REQUEST)
    
    serializer = OrderCreateSerializer(data=request.data, context={'request': request})
    
    if serializer.is_valid():
        # Get delivery address
        address = get_object_or_404(Address, id=serializer.validated_data['delivery_address_id'], user=request.user)
        
        # Get restaurant from cart items (assuming all items are from same restaurant)
        restaurant = cart.items.first().food_item.restaurant
        
        # Calculate totals
        subtotal = cart.total_amount
        delivery_fee = restaurant.delivery_fee
        tax_amount = subtotal * 0.05  # 5% tax
        total_amount = subtotal + delivery_fee + tax_amount
        
        # Create order
        order = Order.objects.create(
            user=request.user,
            restaurant=restaurant,
            delivery_address=address,
            order_number=f"YP{uuid.uuid4().hex[:8].upper()}",
            subtotal=subtotal,
            delivery_fee=delivery_fee,
            tax_amount=tax_amount,
            total_amount=total_amount,
            payment_method=serializer.validated_data['payment_method'],
            special_instructions=serializer.validated_data.get('special_instructions', ''),
            estimated_delivery_time=timezone.now() + timedelta(minutes=restaurant.delivery_time)
        )
        
        # Create order items from cart items
        for cart_item in cart.items.all():
            OrderItem.objects.create(
                order=order,
                food_item=cart_item.food_item,
                quantity=cart_item.quantity,
                price=cart_item.food_item.final_price,
                special_instructions=cart_item.special_instructions
            )
        
        # Clear cart
        cart.items.all().delete()
        
        return Response(OrderSerializer(order).data, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
