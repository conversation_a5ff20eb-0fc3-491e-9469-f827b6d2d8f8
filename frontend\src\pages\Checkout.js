import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  Alert,
  CircularProgress,
} from '@mui/material';
import { LocationOn, Payment } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { addressAPI, orderAPI } from '../services/api';

const Checkout = () => {
  const navigate = useNavigate();
  const { cart, clearCart } = useCart();
  const [addresses, setAddresses] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      const response = await addressAPI.getAll();
      setAddresses(response.data);
      
      // Auto-select default address
      const defaultAddress = response.data.find(addr => addr.is_default);
      if (defaultAddress) {
        setSelectedAddress(defaultAddress.id.toString());
      }
    } catch (error) {
      console.error('Error fetching addresses:', error);
    }
  };

  const handlePlaceOrder = async () => {
    if (!selectedAddress) {
      setError('Please select a delivery address');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const orderData = {
        delivery_address_id: parseInt(selectedAddress),
        payment_method: paymentMethod,
        special_instructions: specialInstructions,
      };

      const response = await orderAPI.create(orderData);
      
      // Clear cart and navigate to orders
      await clearCart();
      navigate('/orders', { 
        state: { 
          newOrder: response.data,
          message: 'Order placed successfully!' 
        }
      });
    } catch (error) {
      setError(error.response?.data?.error || 'Failed to place order');
    } finally {
      setLoading(false);
    }
  };

  if (!cart || cart.items?.length === 0) {
    return (
      <Container maxWidth="md" sx={{ py: 8, textAlign: 'center' }}>
        <Typography variant="h5" gutterBottom>
          Your cart is empty
        </Typography>
        <Button
          variant="contained"
          onClick={() => navigate('/restaurants')}
        >
          Browse Restaurants
        </Button>
      </Container>
    );
  }

  const subtotal = parseFloat(cart.total_amount);
  const deliveryFee = parseFloat(cart.items?.[0]?.food_item?.restaurant?.delivery_fee || 0);
  const taxAmount = subtotal * 0.05;
  const total = subtotal + deliveryFee + taxAmount;

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
        Checkout
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          {/* Delivery Address */}
          <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              <LocationOn sx={{ mr: 1, verticalAlign: 'middle' }} />
              Delivery Address
            </Typography>
            
            {addresses.length > 0 ? (
              <FormControl fullWidth>
                <InputLabel>Select Address</InputLabel>
                <Select
                  value={selectedAddress}
                  onChange={(e) => setSelectedAddress(e.target.value)}
                  label="Select Address"
                >
                  {addresses.map((address) => (
                    <MenuItem key={address.id} value={address.id.toString()}>
                      <Box>
                        <Typography variant="subtitle2">{address.title}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {address.address_line_1}, {address.city}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : (
              <Alert severity="warning">
                No addresses found. Please add an address in your profile.
              </Alert>
            )}
          </Paper>

          {/* Payment Method */}
          <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              <Payment sx={{ mr: 1, verticalAlign: 'middle' }} />
              Payment Method
            </Typography>
            
            <RadioGroup
              value={paymentMethod}
              onChange={(e) => setPaymentMethod(e.target.value)}
            >
              <FormControlLabel
                value="cash"
                control={<Radio />}
                label="Cash on Delivery"
              />
              <FormControlLabel
                value="card"
                control={<Radio />}
                label="Credit/Debit Card"
              />
              <FormControlLabel
                value="upi"
                control={<Radio />}
                label="UPI"
              />
              <FormControlLabel
                value="wallet"
                control={<Radio />}
                label="Digital Wallet"
              />
            </RadioGroup>
          </Paper>

          {/* Special Instructions */}
          <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              Special Instructions
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Any special instructions for your order..."
              value={specialInstructions}
              onChange={(e) => setSpecialInstructions(e.target.value)}
            />
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          {/* Order Summary */}
          <Paper elevation={2} sx={{ p: 3, borderRadius: 2, position: 'sticky', top: 100 }}>
            <Typography variant="h6" gutterBottom>
              Order Summary
            </Typography>
            
            {/* Cart Items */}
            {cart.items?.map((item) => (
              <Box key={item.id} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">
                    {item.food_item.name} x {item.quantity}
                  </Typography>
                  <Typography variant="body2">
                    ₹{item.total_price}
                  </Typography>
                </Box>
              </Box>
            ))}
            
            <Divider sx={{ my: 2 }} />
            
            {/* Pricing Breakdown */}
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Subtotal</Typography>
                <Typography>₹{subtotal.toFixed(2)}</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Delivery Fee</Typography>
                <Typography>₹{deliveryFee.toFixed(2)}</Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>Taxes & Fees</Typography>
                <Typography>₹{taxAmount.toFixed(2)}</Typography>
              </Box>
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Total
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                ₹{total.toFixed(2)}
              </Typography>
            </Box>
            
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handlePlaceOrder}
              disabled={loading || !selectedAddress}
              sx={{ py: 1.5 }}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Place Order'
              )}
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Checkout;
