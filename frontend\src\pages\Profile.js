import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  Avatar,
  Divider,
  Card,
  CardContent,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
} from '@mui/material';
import { Edit, Add, Delete, LocationOn } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { addressAPI } from '../services/api';

const Profile = () => {
  const { user, updateProfile } = useAuth();
  const [addresses, setAddresses] = useState([]);
  const [editMode, setEditMode] = useState(false);
  const [addressDialog, setAddressDialog] = useState({ open: false, address: null });
  const [profileData, setProfileData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
  });
  const [newAddress, setNewAddress] = useState({
    title: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    postal_code: '',
  });
  const [message, setMessage] = useState({ text: '', severity: 'success' });

  useEffect(() => {
    if (user) {
      setProfileData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        phone: user.phone || '',
      });
    }
    fetchAddresses();
  }, [user]);

  const fetchAddresses = async () => {
    try {
      const response = await addressAPI.getAll();
      setAddresses(response.data);
    } catch (error) {
      console.error('Error fetching addresses:', error);
    }
  };

  const handleProfileUpdate = async () => {
    const result = await updateProfile(profileData);
    if (result.success) {
      setMessage({ text: 'Profile updated successfully!', severity: 'success' });
      setEditMode(false);
    } else {
      setMessage({ text: 'Failed to update profile', severity: 'error' });
    }
  };

  const handleAddressSubmit = async () => {
    try {
      if (addressDialog.address) {
        await addressAPI.update(addressDialog.address.id, newAddress);
        setMessage({ text: 'Address updated successfully!', severity: 'success' });
      } else {
        await addressAPI.create(newAddress);
        setMessage({ text: 'Address added successfully!', severity: 'success' });
      }
      
      setAddressDialog({ open: false, address: null });
      setNewAddress({
        title: '',
        address_line_1: '',
        address_line_2: '',
        city: '',
        state: '',
        postal_code: '',
      });
      fetchAddresses();
    } catch (error) {
      setMessage({ text: 'Failed to save address', severity: 'error' });
    }
  };

  const handleDeleteAddress = async (addressId) => {
    if (window.confirm('Are you sure you want to delete this address?')) {
      try {
        await addressAPI.delete(addressId);
        setMessage({ text: 'Address deleted successfully!', severity: 'success' });
        fetchAddresses();
      } catch (error) {
        setMessage({ text: 'Failed to delete address', severity: 'error' });
      }
    }
  };

  const openAddressDialog = (address = null) => {
    if (address) {
      setNewAddress({
        title: address.title,
        address_line_1: address.address_line_1,
        address_line_2: address.address_line_2,
        city: address.city,
        state: address.state,
        postal_code: address.postal_code,
      });
    }
    setAddressDialog({ open: true, address });
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
        My Profile
      </Typography>

      {message.text && (
        <Alert 
          severity={message.severity} 
          sx={{ mb: 3 }}
          onClose={() => setMessage({ text: '', severity: 'success' })}
        >
          {message.text}
        </Alert>
      )}

      <Grid container spacing={4}>
        {/* Profile Information */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">Profile Information</Typography>
              <Button
                startIcon={<Edit />}
                onClick={() => setEditMode(!editMode)}
                variant={editMode ? 'contained' : 'outlined'}
              >
                {editMode ? 'Cancel' : 'Edit'}
              </Button>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar
                sx={{ width: 80, height: 80, mr: 3 }}
                src={user?.profile_picture}
              >
                {user?.first_name?.[0]}{user?.last_name?.[0]}
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {user?.first_name} {user?.last_name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.email}
                </Typography>
                <Chip
                  label={user?.user_type?.replace('_', ' ')}
                  size="small"
                  color="primary"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Box>

            {editMode ? (
              <Box>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="First Name"
                      value={profileData.first_name}
                      onChange={(e) => setProfileData({...profileData, first_name: e.target.value})}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      value={profileData.last_name}
                      onChange={(e) => setProfileData({...profileData, last_name: e.target.value})}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Phone"
                      value={profileData.phone}
                      onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                    />
                  </Grid>
                </Grid>
                <Button
                  variant="contained"
                  onClick={handleProfileUpdate}
                  sx={{ mt: 2 }}
                >
                  Save Changes
                </Button>
              </Box>
            ) : (
              <Box>
                <Typography variant="body1" gutterBottom>
                  <strong>Phone:</strong> {user?.phone || 'Not provided'}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>Member since:</strong> {new Date(user?.date_joined).toLocaleDateString()}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Addresses */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                <LocationOn sx={{ mr: 1, verticalAlign: 'middle' }} />
                Delivery Addresses
              </Typography>
              <Button
                startIcon={<Add />}
                onClick={() => openAddressDialog()}
                variant="outlined"
              >
                Add Address
              </Button>
            </Box>

            {addresses.length === 0 ? (
              <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                No addresses added yet
              </Typography>
            ) : (
              addresses.map((address) => (
                <Card key={address.id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {address.title}
                          {address.is_default && (
                            <Chip label="Default" size="small" color="primary" sx={{ ml: 1 }} />
                          )}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {address.address_line_1}
                          {address.address_line_2 && <>, {address.address_line_2}</>}
                          <br />
                          {address.city}, {address.state} {address.postal_code}
                        </Typography>
                      </Box>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => openAddressDialog(address)}
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteAddress(address.id)}
                        >
                          <Delete />
                        </IconButton>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Address Dialog */}
      <Dialog 
        open={addressDialog.open} 
        onClose={() => setAddressDialog({ open: false, address: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {addressDialog.address ? 'Edit Address' : 'Add New Address'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address Title"
                value={newAddress.title}
                onChange={(e) => setNewAddress({...newAddress, title: e.target.value})}
                placeholder="e.g., Home, Work"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address Line 1"
                value={newAddress.address_line_1}
                onChange={(e) => setNewAddress({...newAddress, address_line_1: e.target.value})}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address Line 2"
                value={newAddress.address_line_2}
                onChange={(e) => setNewAddress({...newAddress, address_line_2: e.target.value})}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="City"
                value={newAddress.city}
                onChange={(e) => setNewAddress({...newAddress, city: e.target.value})}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="State"
                value={newAddress.state}
                onChange={(e) => setNewAddress({...newAddress, state: e.target.value})}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Postal Code"
                value={newAddress.postal_code}
                onChange={(e) => setNewAddress({...newAddress, postal_code: e.target.value})}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddressDialog({ open: false, address: null })}>
            Cancel
          </Button>
          <Button onClick={handleAddressSubmit} variant="contained">
            {addressDialog.address ? 'Update' : 'Add'} Address
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Profile;
