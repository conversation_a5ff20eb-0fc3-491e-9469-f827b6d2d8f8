version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - media_volume:/app/media
    environment:
      - DEBUG=True
    depends_on:
      - db

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: yourplace
      POSTGRES_USER: yourplace
      POSTGRES_PASSWORD: yourplace123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
  media_volume:
