from django.urls import path
from . import views

urlpatterns = [
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('', views.RestaurantListView.as_view(), name='restaurant_list'),
    path('create/', views.RestaurantCreateView.as_view(), name='restaurant_create'),
    path('<int:pk>/', views.RestaurantDetailView.as_view(), name='restaurant_detail'),
    path('<int:restaurant_id>/food-items/', views.RestaurantFoodItemsView.as_view(), name='restaurant_food_items'),
    path('<int:restaurant_id>/reviews/', views.ReviewListCreateView.as_view(), name='restaurant_reviews'),
    path('food-items/', views.FoodItemListView.as_view(), name='food_item_list'),
    path('food-items/<int:pk>/', views.FoodItemDetailView.as_view(), name='food_item_detail'),
    path('search/', views.search_restaurants, name='search_restaurants'),
]
