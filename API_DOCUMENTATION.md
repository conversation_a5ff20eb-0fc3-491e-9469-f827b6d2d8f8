# YourPlace API Documentation

## Base URL
```
http://localhost:8000/api
```

## Authentication
The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Endpoints

### Authentication
- `POST /auth/register/` - Register a new user
- `POST /auth/login/` - Login user
- `POST /auth/token/refresh/` - Refresh access token
- `GET /auth/profile/` - Get user profile
- `PATCH /auth/profile/` - Update user profile

### Addresses
- `GET /auth/addresses/` - Get user addresses
- `POST /auth/addresses/` - Create new address
- `PATCH /auth/addresses/{id}/` - Update address
- `DELETE /auth/addresses/{id}/` - Delete address

### Restaurants
- `GET /restaurants/` - List all restaurants (with filters)
- `GET /restaurants/{id}/` - Get restaurant details
- `GET /restaurants/categories/` - List food categories
- `GET /restaurants/{id}/food-items/` - Get restaurant menu
- `GET /restaurants/search/` - Search restaurants

### Cart
- `GET /orders/cart/` - Get user cart
- `POST /orders/cart/add/` - Add item to cart
- `PUT /orders/cart/update/{item_id}/` - Update cart item
- `DELETE /orders/cart/remove/{item_id}/` - Remove item from cart
- `DELETE /orders/cart/clear/` - Clear entire cart

### Orders
- `GET /orders/` - Get user orders
- `GET /orders/{id}/` - Get order details
- `POST /orders/create/` - Create new order

### Core
- `GET /home/<USER>
- `GET /stats/` - Get app statistics

## Sample Requests

### Register User
```json
POST /auth/register/
{
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+1234567890",
    "user_type": "customer",
    "password": "securepassword",
    "password_confirm": "securepassword"
}
```

### Login
```json
POST /auth/login/
{
    "email": "<EMAIL>",
    "password": "securepassword"
}
```

### Add to Cart
```json
POST /orders/cart/add/
{
    "food_item": 1,
    "quantity": 2,
    "special_instructions": "Extra spicy"
}
```

### Create Order
```json
POST /orders/create/
{
    "delivery_address_id": 1,
    "payment_method": "cash",
    "special_instructions": "Ring the bell twice"
}
```

## Response Format

### Success Response
```json
{
    "data": {...},
    "message": "Success message"
}
```

### Error Response
```json
{
    "error": "Error message",
    "details": {...}
}
```

## Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error
