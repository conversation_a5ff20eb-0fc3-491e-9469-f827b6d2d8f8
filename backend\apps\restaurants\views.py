from rest_framework import generics, filters, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Avg
from .models import Restaurant, FoodItem, Category, Review
from .serializers import (
    RestaurantListSerializer, 
    RestaurantDetailSerializer,
    RestaurantCreateSerializer,
    FoodItemSerializer,
    CategorySerializer,
    ReviewSerializer
)

class CategoryListView(generics.ListAPIView):
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]

class RestaurantListView(generics.ListAPIView):
    serializer_class = RestaurantListSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['cuisine_types', 'city', 'is_featured']
    search_fields = ['name', 'description', 'cuisine_types__name']
    ordering_fields = ['rating', 'delivery_time', 'delivery_fee', 'created_at']
    ordering = ['-rating']
    
    def get_queryset(self):
        return Restaurant.objects.filter(is_active=True)

class RestaurantDetailView(generics.RetrieveAPIView):
    queryset = Restaurant.objects.filter(is_active=True)
    serializer_class = RestaurantDetailSerializer
    permission_classes = [permissions.AllowAny]

class RestaurantCreateView(generics.CreateAPIView):
    serializer_class = RestaurantCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

class FoodItemListView(generics.ListAPIView):
    serializer_class = FoodItemSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['restaurant', 'category', 'food_type', 'is_featured']
    search_fields = ['name', 'description', 'ingredients']
    ordering_fields = ['price', 'created_at']
    ordering = ['price']
    
    def get_queryset(self):
        return FoodItem.objects.filter(is_available=True)

class FoodItemDetailView(generics.RetrieveAPIView):
    queryset = FoodItem.objects.filter(is_available=True)
    serializer_class = FoodItemSerializer
    permission_classes = [permissions.AllowAny]

class RestaurantFoodItemsView(generics.ListAPIView):
    serializer_class = FoodItemSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        restaurant_id = self.kwargs['restaurant_id']
        return FoodItem.objects.filter(restaurant_id=restaurant_id, is_available=True)

class ReviewListCreateView(generics.ListCreateAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        restaurant_id = self.kwargs['restaurant_id']
        return Review.objects.filter(restaurant_id=restaurant_id)
    
    def perform_create(self, serializer):
        restaurant_id = self.kwargs['restaurant_id']
        restaurant = Restaurant.objects.get(id=restaurant_id)
        review = serializer.save(user=self.request.user, restaurant=restaurant)
        
        # Update restaurant rating
        avg_rating = Review.objects.filter(restaurant=restaurant).aggregate(
            avg_rating=Avg('rating')
        )['avg_rating']
        restaurant.rating = round(avg_rating, 2) if avg_rating else 0
        restaurant.total_reviews = Review.objects.filter(restaurant=restaurant).count()
        restaurant.save()

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def search_restaurants(request):
    query = request.GET.get('q', '')
    city = request.GET.get('city', '')
    
    restaurants = Restaurant.objects.filter(is_active=True)
    
    if query:
        restaurants = restaurants.filter(
            Q(name__icontains=query) | 
            Q(description__icontains=query) |
            Q(cuisine_types__name__icontains=query)
        ).distinct()
    
    if city:
        restaurants = restaurants.filter(city__icontains=city)
    
    serializer = RestaurantListSerializer(restaurants, many=True)
    return Response(serializer.data)
