from rest_framework import serializers
from .models import Cart, CartItem, Order, OrderItem
from apps.restaurants.serializers import FoodItemSerializer
from apps.users.serializers import AddressSerializer

class CartItemSerializer(serializers.ModelSerializer):
    food_item = FoodItemSerializer(read_only=True)
    food_item_id = serializers.IntegerField(write_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = CartItem
        fields = '__all__'
        read_only_fields = ('id', 'cart', 'created_at', 'updated_at')

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_items = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Cart
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at', 'updated_at')

class OrderItemSerializer(serializers.ModelSerializer):
    food_item_name = serializers.CharField(source='food_item.name', read_only=True)
    food_item_image = serializers.ImageField(source='food_item.image', read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    
    class Meta:
        model = OrderItem
        fields = '__all__'

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    restaurant_image = serializers.ImageField(source='restaurant.image', read_only=True)
    delivery_address = AddressSerializer(read_only=True)
    
    class Meta:
        model = Order
        fields = '__all__'
        read_only_fields = ('id', 'user', 'order_number', 'created_at', 'updated_at')

class OrderCreateSerializer(serializers.ModelSerializer):
    delivery_address_id = serializers.IntegerField()
    
    class Meta:
        model = Order
        fields = ('delivery_address_id', 'payment_method', 'special_instructions')
    
    def validate_delivery_address_id(self, value):
        user = self.context['request'].user
        try:
            address = user.addresses.get(id=value)
            return value
        except:
            raise serializers.ValidationError("Invalid delivery address")

class CartItemCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CartItem
        fields = ('food_item', 'quantity', 'special_instructions')
    
    def validate_food_item(self, value):
        if not value.is_available:
            raise serializers.ValidationError("This food item is not available")
        return value
